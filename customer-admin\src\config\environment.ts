/**
 * Environment Configuration
 * 
 * This file handles environment variable configuration for both build-time and runtime.
 * It provides fallbacks for missing environment variables and validates required values.
 */

// Required environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL',
  'NEXT_PUBLIC_AUTH_FRONTEND_URL',
  'NEXT_PUBLIC_AUTH_JWKS_URL',
  'NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY'
] as const;

// Environment configuration with fallbacks
export const env = {
  // Customer API Configuration
  CUSTOMER_GRAPHQL_URL: process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL || 'https://ng-customer-dev.dev1.ngnair.com/graphql',
  CUSTOMER_API_URL: process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'https://ng-customer-dev.dev1.ngnair.com',
  
  // Authentication Configuration
  AUTH_FRONTEND_URL: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com',
  AUTH_JWKS_URL: process.env.NEXT_PUBLIC_AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
  ACCESS_TOKEN_ENCRYPTION_KEY: process.env.NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
  
  // Application Configuration
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'Customer Management System',
  SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://ng-customer-admin-dev.dev1.ngnair.com',
  ADMIN_EMAIL: process.env.NEXT_PUBLIC_ADMIN_EMAIL || '<EMAIL>',
  IMAGE_DOMAINS: process.env.NEXT_PUBLIC_IMAGE_DOMAINS || 'ng-customer-dev.dev1.ngnair.com',
  
  // Development Configuration
  NODE_ENV: process.env.NODE_ENV || 'development',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  
  // Debug Configuration
  AUTH_DEBUG_MODE: process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true',
};

/**
 * Validate required environment variables
 */
export function validateEnvironment(): { isValid: boolean; missingVars: string[] } {
  const missingVars: string[] = [];
  
  for (const varName of requiredEnvVars) {
    const value = process.env[varName];
    if (!value || value.trim() === '') {
      missingVars.push(varName);
    }
  }
  
  return {
    isValid: missingVars.length === 0,
    missingVars
  };
}

/**
 * Log environment configuration (for debugging)
 */
export function logEnvironmentConfig(): void {
  if (typeof window !== 'undefined') {
    console.log('🔧 Environment Configuration:');
    console.log('Running in browser');
    console.log('Customer GraphQL URL:', env.CUSTOMER_GRAPHQL_URL);
    console.log('Auth Frontend URL:', env.AUTH_FRONTEND_URL);
    console.log('Auth JWKS URL:', env.AUTH_JWKS_URL);
    console.log('Site URL:', env.SITE_URL);
    console.log('Node Environment:', env.NODE_ENV);
    console.log('Debug Mode:', env.AUTH_DEBUG_MODE);
    
    // Validate environment
    const validation = validateEnvironment();
    if (!validation.isValid) {
      console.error('❌ Missing required environment variables:', validation.missingVars);
      validation.missingVars.forEach(varName => {
        console.error(`❌ ${varName} is missing`);
      });
    } else {
      console.log('✅ All required environment variables are present');
    }
  }
}

/**
 * Get environment variable with error handling
 */
export function getEnvVar(key: keyof typeof env, required: boolean = false): string {
  const value = env[key];
  
  if (required && (!value || value.trim() === '')) {
    const errorMessage = `${key} environment variable is required`;
    console.error(`❌ ${errorMessage}`);
    throw new Error(errorMessage);
  }
  
  return value || '';
}

/**
 * Check if we're running in the browser
 */
export const isBrowser = typeof window !== 'undefined';

/**
 * Check if we're running in development mode
 */
export const isDevelopment = env.IS_DEVELOPMENT;

/**
 * Check if we're running in production mode
 */
export const isProduction = env.IS_PRODUCTION;

// Export individual environment variables for convenience
export const {
  CUSTOMER_GRAPHQL_URL,
  CUSTOMER_API_URL,
  AUTH_FRONTEND_URL,
  AUTH_JWKS_URL,
  ACCESS_TOKEN_ENCRYPTION_KEY,
  APP_NAME,
  SITE_URL,
  ADMIN_EMAIL,
  IMAGE_DOMAINS,
  NODE_ENV,
  AUTH_DEBUG_MODE
} = env;

// Validate environment on module load (only in browser)
if (isBrowser) {
  logEnvironmentConfig();
}
