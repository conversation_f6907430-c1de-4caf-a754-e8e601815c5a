"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthResolver_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const auth_service_1 = require("../../auth-shared/auth.service");
const auth_types_1 = require("../types/auth.types");
let AuthResolver = AuthResolver_1 = class AuthResolver {
    authService;
    logger = new common_1.Logger(AuthResolver_1.name);
    constructor(authService) {
        this.authService = authService;
    }
    async logout(context) {
        this.logger.log('🔐 [AUTH RESOLVER] Logout mutation called');
        const response = context.res;
        if (!response) {
            this.logger.error('❌ [AUTH RESOLVER] Response context not available for logout');
            throw new Error('Response context not available');
        }
        try {
            response.cookie('access_token', '', {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                domain: process.env.COOKIE_DOMAIN || undefined,
                path: '/',
                expires: new Date(0)
            });
            response.cookie('refresh_token', '', {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                domain: process.env.COOKIE_DOMAIN || undefined,
                path: '/',
                expires: new Date(0)
            });
            this.logger.log('✅ [AUTH RESOLVER] Authentication cookies cleared successfully');
            return true;
        }
        catch (error) {
            this.logger.error(`❌ [AUTH RESOLVER] Error clearing cookies: ${error.message}`);
            throw new Error('Failed to logout');
        }
    }
    async getCurrentUserFromCookies(context) {
        this.logger.log('🔍 [AUTH RESOLVER] meCookies query called');
        const request = context.req;
        if (!request) {
            this.logger.error('❌ [AUTH RESOLVER] Request context not available');
            throw new Error('Request context not available');
        }
        this.logger.log('🌐 [AUTH RESOLVER] Request details:');
        this.logger.log(`  - Origin: ${request.headers.origin || 'NONE'}`);
        this.logger.log(`  - User-Agent: ${request.headers['user-agent']?.substring(0, 50) || 'NONE'}...`);
        this.logger.log(`  - Cookie header present: ${!!request.headers.cookie}`);
        this.logger.log(`  - Raw cookie header: ${request.headers.cookie || 'NONE'}`);
        const cookies = request.cookies || {};
        this.logger.log(`🍪 [AUTH RESOLVER] Parsed cookies: ${Object.keys(cookies).join(', ') || 'NONE'}`);
        this.logger.log('🍪 [AUTH RESOLVER] Cookie details:');
        this.logger.log(`  - access_token present: ${!!cookies.access_token}`);
        this.logger.log(`  - refresh_token present: ${!!cookies.refresh_token}`);
        this.logger.log(`  - access_token value: ${cookies.access_token ? `${cookies.access_token.substring(0, 20)}...` : 'MISSING'}`);
        this.logger.log(`  - refresh_token value: ${cookies.refresh_token ? `${cookies.refresh_token.substring(0, 20)}...` : 'MISSING'}`);
        if (!cookies.access_token && !cookies.refresh_token) {
            this.logger.warn('❌ [AUTH RESOLVER] No authentication cookies found');
            throw new Error('Authentication failed - no cookies');
        }
        try {
            this.logger.log('🔐 [AUTH RESOLVER] Attempting to validate user from cookies...');
            const user = await this.authService.authenticateFromCookies(cookies);
            if (!user) {
                this.logger.error('❌ [AUTH RESOLVER] User validation failed - no user returned');
                throw new Error('Authentication failed - no user');
            }
            this.logger.log(`✅ [AUTH RESOLVER] User authenticated successfully: ${user.email} (${user.role})`);
            return user;
        }
        catch (error) {
            this.logger.error(`❌ [AUTH RESOLVER] Authentication error: ${error.message}`);
            throw error;
        }
    }
    async refreshAuthentication(context) {
        this.logger.log('GraphQL: Refreshing authentication');
        const cookies = context.req.cookies || {};
        return await this.authService.refreshAuthentication(cookies);
    }
    async getAuthStatus() {
        this.logger.log('GraphQL: Getting auth status');
        return {
            status: 'ok',
            service: 'auth-shared',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
        };
    }
    async decryptToken(input) {
        this.logger.log('GraphQL: Decrypting token for testing');
        try {
            const decryptedToken = await this.authService.decryptTokenForTesting(input.token);
            let tokenType = 'unknown';
            try {
                const parsed = JSON.parse(decryptedToken);
                if (parsed.exp && parsed.iat) {
                    tokenType = 'jwt';
                }
            }
            catch {
                if (decryptedToken.includes('.')) {
                    tokenType = 'jwt';
                }
            }
            return {
                decryptedToken,
                tokenType,
                success: true,
            };
        }
        catch (error) {
            return {
                decryptedToken: '',
                tokenType: 'unknown',
                success: false,
                error: error.message,
            };
        }
    }
    async decodeJwt(input) {
        this.logger.log('GraphQL: Decoding JWT for testing');
        try {
            const jwt = require('jsonwebtoken');
            const decoded = jwt.decode(input.jwt, { complete: true });
            if (!decoded) {
                throw new Error('Invalid JWT format');
            }
            return {
                header: JSON.stringify(decoded.header),
                payload: JSON.stringify(decoded.payload),
                signature: decoded.signature || '',
                success: true,
            };
        }
        catch (error) {
            return {
                header: '',
                payload: '',
                signature: '',
                success: false,
                error: error.message,
            };
        }
    }
};
exports.AuthResolver = AuthResolver;
__decorate([
    (0, graphql_1.Mutation)(() => Boolean, { name: 'logout', description: 'Logout user by clearing authentication cookies' }),
    __param(0, (0, graphql_1.Context)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "logout", null);
__decorate([
    (0, graphql_1.Query)(() => auth_types_1.User, { name: 'meCookies', description: 'Get current user from encrypted cookies' }),
    __param(0, (0, graphql_1.Context)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "getCurrentUserFromCookies", null);
__decorate([
    (0, graphql_1.Mutation)(() => Boolean, { name: 'refreshAuth', description: 'Refresh authentication tokens' }),
    __param(0, (0, graphql_1.Context)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "refreshAuthentication", null);
__decorate([
    (0, graphql_1.Query)(() => auth_types_1.AuthStatus, { name: 'authStatus', description: 'Get authentication service status' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "getAuthStatus", null);
__decorate([
    (0, graphql_1.Mutation)(() => auth_types_1.DecryptTokenResponse, { name: 'decryptToken', description: 'Decrypt encrypted token (testing)' }),
    __param(0, (0, graphql_1.Args)('input')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_types_1.DecryptTokenInput]),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "decryptToken", null);
__decorate([
    (0, graphql_1.Mutation)(() => auth_types_1.DecodeJwtResponse, { name: 'decodeJwt', description: 'Decode JWT token (testing)' }),
    __param(0, (0, graphql_1.Args)('input')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_types_1.DecodeJwtInput]),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "decodeJwt", null);
exports.AuthResolver = AuthResolver = AuthResolver_1 = __decorate([
    (0, graphql_1.Resolver)(),
    __metadata("design:paramtypes", [auth_service_1.AuthSharedService])
], AuthResolver);
//# sourceMappingURL=auth.resolver.js.map