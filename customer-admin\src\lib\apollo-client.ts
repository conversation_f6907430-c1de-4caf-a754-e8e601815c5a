import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { CUSTOMER_GRAPHQL_URL, getEnvVar } from '../config/environment';

// Validate GraphQL URL
const graphqlUrl = getEnvVar('CUSTOMER_GRAPHQL_URL', false) || CUSTOMER_GRAPHQL_URL;

// Create HTTP link
const httpLink = createHttpLink({
  uri: graphqlUrl,
  credentials: 'include', // Include cookies for authentication
});

// Auth link for GraphQL requests - cookies are automatically included
const authLink = setContext((_, { headers }) => {
  return {
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    }
  }
});

// Create Apollo Client
export const apolloClient = new ApolloClient({
  link: from([authLink, httpLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
  },
});
