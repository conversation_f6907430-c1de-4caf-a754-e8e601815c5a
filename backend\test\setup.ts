import { PrismaClient } from '@prisma/client';

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://postgres:password@localhost:5432/customer_service_test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.AUTH_SERVICE_URL = 'http://localhost:3001';
});

// Clean up after all tests
afterAll(async () => {
  const prisma = new PrismaClient();
  
  // Clean up test data
  await prisma.auditLog.deleteMany();
  await prisma.customerSegmentMember.deleteMany();
  await prisma.customerSegment.deleteMany();
  await prisma.customerPreference.deleteMany();
  await prisma.paymentToken.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.address.deleteMany();
  await prisma.customer.deleteMany();
  
  await prisma.$disconnect();
});

// Mock external services
jest.mock('../src/modules/auth-shared/auth.service', () => ({
  AuthSharedService: jest.fn().mockImplementation(() => ({
    validateToken: jest.fn().mockResolvedValue({
      id: 'test-user-id',
      email: '<EMAIL>',
      isAdmin: false,
      partnerId: 'test-partner-id',
    }),
    authenticateFromCookies: jest.fn().mockResolvedValue({
      id: 'test-user-id',
      email: '<EMAIL>',
      isAdmin: false,
      partnerId: 'test-partner-id',
    }),
    isAdmin: jest.fn().mockReturnValue(false),
  })),
}));

// Mock logger
jest.mock('@nestjs/common', () => ({
  ...jest.requireActual('@nestjs/common'),
  Logger: jest.fn().mockImplementation(() => ({
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  })),
}));
