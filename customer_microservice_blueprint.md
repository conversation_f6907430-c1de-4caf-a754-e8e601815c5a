# NGnair Customer Microservice Blueprint

## How the Customer Microservice is Built

### **🏗️ Architecture Overview**

**Technology Stack**:
- **Backend Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **API Layer**: GraphQL with Apollo Server
- **Authentication**: JWT tokens with external auth service integration
- **Containerization**: Docker with multi-stage builds
- **Process Management**: PM2 for production deployment

**Project Structure**:
```
backend/
├── src/
│   ├── modules/           # Feature modules
│   │   ├── customers-management/    # Core customer CRUD operations
│   │   ├── customers-query/         # Customer search and filtering
│   │   ├── customers-admin/         # Admin-specific operations
│   │   ├── customers-verification/  # Email/phone/KYC verification
│   │   ├── customers-audit/         # Audit logging
│   │   ├── customers-access-control/ # Authorization
│   │   ├── payment-tokens/          # Payment token management
│   │   ├── transactions/            # Transaction processing
│   │   ├── graphql/                 # GraphQL schema and resolvers
│   │   └── shared/                  # Shared utilities
│   ├── prisma/            # Database schema and migrations
│   ├── webhook/           # Webhook processing
│   └── config/            # Configuration management
├── prisma/
│   ├── schema.prisma      # Database schema
│   └── migrations/        # Database migrations
└── docker-compose.yml     # Local development setup
```

### **🗄️ Database Design**

**Core Tables**:
- **customers**: Main customer entity with personal/business information
- **addresses**: Customer addresses with type classification
- **contacts**: Emergency and business contacts
- **customer_preferences**: Communication and privacy preferences
- **payment_tokens**: Secure payment method token storage
- **audit_logs**: Complete audit trail of all changes
- **customer_segments**: Customer grouping and segmentation
- **customer_segment_members**: Many-to-many relationship for segments

**Key Database Features**:
- **Indexing Strategy**: Optimized indexes for common query patterns
- **Soft Deletes**: Maintains data integrity with soft delete patterns
- **JSON Storage**: Flexible metadata storage for provider-specific data
- **Constraints**: Database-level constraints for data integrity
- **Migrations**: Version-controlled database schema changes

### **🔌 API Design**

**GraphQL Schema**:
- **Queries**: Customer search, filtering, and retrieval operations
- **Mutations**: Customer creation, updates, and verification operations
- **Types**: Strongly typed schema with comprehensive field definitions
- **Resolvers**: Efficient data fetching with N+1 query prevention
- **Subscriptions**: Real-time updates for customer data changes

**REST Endpoints**:
- **Webhook Endpoints**: `/webhook/*` for payment provider integrations
- **Health Checks**: `/health` for service monitoring
- **Metrics**: `/metrics` for performance monitoring

### **🔐 Security Implementation**

**Authentication Flow**:
1. External auth service validates JWT tokens
2. Token verification via JWKS endpoint
3. User permissions extracted from token claims
4. Role-based access control applied to operations

**Data Protection**:
- **Token Hashing**: Payment tokens stored as secure hashes
- **Encryption**: Sensitive data encrypted at rest
- **Access Logging**: All data access logged for audit
- **Input Validation**: Comprehensive input validation and sanitization
- **Rate Limiting**: API rate limiting to prevent abuse

### **📦 Deployment Architecture**

**Container Setup**:
- **Multi-stage Docker builds** for optimized production images
- **Environment-based configuration** via environment variables
- **Health checks** for container orchestration
- **Resource limits** for memory and CPU usage

**Environment Configuration**:
- **Development**: Local development with hot reload
- **Staging**: Production-like environment for testing
- **Production**: High-availability production deployment

**Monitoring & Logging**:
- **Application Logs**: Structured logging with Winston
- **Performance Metrics**: Custom metrics for business operations
- **Error Tracking**: Comprehensive error logging and alerting
- **Health Monitoring**: Service health checks and uptime monitoring

### **🔄 Data Flow Architecture**

**Webhook Processing Flow**:
1. Payment provider sends webhook to `/webhook/payment`
2. Webhook service validates and parses payload
3. Customer data extracted and normalized
4. Payment token created or updated
5. Audit log entry created
6. Real-time notifications sent to subscribers

**Customer Management Flow**:
1. Admin creates/updates customer via GraphQL
2. Input validation and authorization checks
3. Database transaction with audit logging
4. Real-time updates propagated
5. External systems notified via webhooks

### **🧪 Testing Strategy**

**Test Types**:
- **Unit Tests**: Individual function and method testing
- **Integration Tests**: Database and API integration testing
- **E2E Tests**: Complete workflow testing
- **Performance Tests**: Load and stress testing

**Test Configuration**:
- **Jest**: Primary testing framework
- **Test Database**: Isolated test database for integration tests
- **Mocking**: External service mocking for reliable tests
- **Coverage**: Comprehensive code coverage reporting

### **🚀 Development Workflow**

**Local Development**:
1. Clone repository and install dependencies
2. Set up local PostgreSQL database
3. Run database migrations
4. Start development server with hot reload
5. Use GraphQL playground for API testing

**Code Quality**:
- **TypeScript**: Strong typing for better code quality
- **ESLint**: Code linting and style enforcement
- **Prettier**: Automatic code formatting
- **Husky**: Pre-commit hooks for quality checks

**Deployment Process**:
1. Code pushed to repository
2. Automated tests run in CI/CD pipeline
3. Docker image built and tagged
4. Image deployed to target environment
5. Health checks verify successful deployment
