# Customer Microservice GraphQL API Documentation

## Overview

The Customer Microservice provides a comprehensive GraphQL API for managing customer data, authentication, and transaction processing. The API is built with NestJS and Apollo Server, offering both queries and mutations for complete customer lifecycle management.

**GraphQL Endpoint**: `/graphql`
**GraphQL Playground**: Available in development mode at `/graphql`

## Authentication

Most operations require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <your-jwt-token>
```

Some queries are marked as `@Public()` for testing purposes.

## Core Entities

### Customer
- **id**: Unique identifier
- **firstName/lastName**: Customer name
- **email**: Email address (unique)
- **phone**: Phone number
- **status**: ACTIVE, INACTIVE, SUSPENDED, PENDING_VERIFICATION, BLOCKED
- **type**: INDIVIDUAL, BUSINESS, ENTERPRISE
- **addresses**: Array of customer addresses
- **contacts**: Array of emergency/business contacts
- **preferences**: Communication and privacy preferences
- **auditLogs**: Complete audit trail
- **paymentTokens**: Secure payment method tokens

## Queries

### 1. Get Paginated Customers

**Query**: `customers`
**Description**: Retrieve customers with pagination and filtering
**Access**: Public (for testing)

```graphql
query GetCustomers($page: Int, $limit: Int, $filter: String) {
  customers(page: $page, limit: $limit, filter: $filter) {
    data {
      id
      firstName
      lastName
      email
      phone
      status
      type
      createdAt
      updatedAt
    }
    total
    page
    limit
  }
}
```

**Variables**:
```json
{
  "page": 1,
  "limit": 10,
  "filter": "john"
}
```

### 2. Get Single Customer

**Query**: `customer`
**Description**: Retrieve a specific customer by ID
**Access**: Public (for testing)

```graphql
query GetCustomer($id: ID!) {
  customer(id: $id) {
    id
    firstName
    lastName
    email
    phone
    status
    type
    companyName
    businessType
    isEmailVerified
    isPhoneVerified
    isKycVerified
    addresses {
      id
      type
      street1
      street2
      city
      state
      postalCode
      country
      isDefault
    }
    contacts {
      id
      type
      firstName
      lastName
      email
      phone
      relationship
    }
    preferences {
      id
      type
      key
      value
      isActive
    }
    auditLogs {
      id
      action
      entity
      description
      createdAt
    }
    createdAt
    updatedAt
  }
}
```

**Variables**:
```json
{
  "id": "customer_id_here"
}
```

### 3. Get Customer by Email

**Query**: `customerByEmail`
**Description**: Find customer by email address
**Access**: Requires authentication

```graphql
query GetCustomerByEmail($email: String!) {
  customerByEmail(email: $email) {
    id
    firstName
    lastName
    email
    phone
    status
    type
    createdAt
  }
}
```

**Variables**:
```json
{
  "email": "<EMAIL>"
}
```

### 4. Advanced Customer Search

**Query**: `searchCustomers`
**Description**: Advanced search with multiple filters
**Access**: Requires authentication

```graphql
query SearchCustomers($filter: CustomerFilterInput, $pagination: PaginationInput) {
  searchCustomers(filter: $filter, pagination: $pagination) {
    id
    firstName
    lastName
    email
    phone
    status
    type
    companyName
    createdAt
  }
}
```

**Variables**:
```json
{
  "filter": {
    "search": "john",
    "status": "ACTIVE",
    "type": "INDIVIDUAL",
    "isEmailVerified": true,
    "createdAfter": "2024-01-01T00:00:00Z",
    "createdBefore": "2024-12-31T23:59:59Z"
  },
  "pagination": {
    "skip": 0,
    "take": 20
  }
}
```

### 5. Admin Customer List

**Query**: `customersAdmin`
**Description**: Admin-only customer listing with full access
**Access**: Requires admin authentication

```graphql
query GetCustomersAdmin($filter: CustomerFilterInput, $pagination: PaginationInput) {
  customersAdmin(filter: $filter, pagination: $pagination) {
    id
    firstName
    lastName
    email
    phone
    status
    type
    companyName
    businessType
    isEmailVerified
    isPhoneVerified
    isKycVerified
    createdAt
    lastLoginAt
  }
}
```

### 6. Get Current User

**Query**: `me`
**Description**: Get current authenticated user information
**Access**: Requires authentication

```graphql
query GetCurrentUser {
  me {
    id
    email
    firstName
    lastName
    role
    isAdmin
    permissions
    accountId
    partnerId
    active
    verifiedEmail
    verifiedPhone
    mfaEnabled
    createdAt
  }
}
```

## Mutations

### 1. Create Customer

**Mutation**: `createCustomer`
**Description**: Create a new customer record
**Access**: Requires authentication

```graphql
mutation CreateCustomer($input: CreateCustomerInput!) {
  createCustomer(input: $input) {
    id
    firstName
    lastName
    email
    phone
    status
    type
    companyName
    businessType
    createdAt
  }
}
```

**Variables**:
```json
{
  "input": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "type": "INDIVIDUAL",
    "status": "ACTIVE",
    "dateOfBirth": "1990-01-01",
    "companyName": "Acme Corp",
    "businessType": "Technology",
    "taxId": "123-45-6789",
    "notes": "VIP customer",
    "tags": ["vip", "tech"]
  }
}
```

### 2. Update Customer

**Mutation**: `updateCustomer`
**Description**: Update existing customer information
**Access**: Requires authentication

```graphql
mutation UpdateCustomer($input: UpdateCustomerInput!) {
  updateCustomer(input: $input) {
    id
    firstName
    lastName
    email
    phone
    status
    type
    updatedAt
  }
}
```

**Variables**:
```json
{
  "input": {
    "id": "customer_id_here",
    "firstName": "Jane",
    "lastName": "Smith",
    "phone": "+1987654321",
    "notes": "Updated customer information"
  }
}
```

### 3. Delete Customer

**Mutation**: `deleteCustomer`
**Description**: Soft delete a customer record
**Access**: Requires authentication

```graphql
mutation DeleteCustomer($id: ID!) {
  deleteCustomer(id: $id)
}
```

**Variables**:
```json
{
  "id": "customer_id_here"
}
```

### 4. Verify Customer Email

**Mutation**: `verifyCustomerEmail`
**Description**: Mark customer email as verified
**Access**: Requires authentication

```graphql
mutation VerifyCustomerEmail($id: ID!) {
  verifyCustomerEmail(id: $id) {
    id
    email
    isEmailVerified
    updatedAt
  }
}
```

### 5. Verify Customer Phone

**Mutation**: `verifyCustomerPhone`
**Description**: Mark customer phone as verified
**Access**: Requires authentication

```graphql
mutation VerifyCustomerPhone($id: ID!) {
  verifyCustomerPhone(id: $id) {
    id
    phone
    isPhoneVerified
    updatedAt
  }
}
```

### 6. Update Customer Status (Admin)

**Mutation**: `updateCustomerStatus`
**Description**: Admin-only status updates
**Access**: Requires admin authentication

```graphql
mutation UpdateCustomerStatus($id: ID!, $status: String!) {
  updateCustomerStatus(id: $id, status: $status) {
    id
    status
    updatedAt
  }
}
```

**Variables**:
```json
{
  "id": "customer_id_here",
  "status": "SUSPENDED"
}
```

## Transaction Processing

### 7. Initiate Transaction

**Mutation**: `initiateTransaction`
**Description**: Start a transaction and send OTP to user's phone
**Access**: Requires authentication

```graphql
mutation InitiateTransaction($input: TransactionInput!) {
  initiateTransaction(input: $input) {
    transactionId
    status
    message
    userDetails {
      id
      email
      firstName
      lastName
      phone {
        number
        masked
      }
      isAdmin
      permissions
    }
  }
}
```

**Variables**:
```json
{
  "input": {
    "transactionId": "txn_**********",
    "amount": 100.50,
    "currency": "USD",
    "description": "Payment for services"
  }
}
```

### 8. Verify OTP and Complete Transaction

**Mutation**: `verifyOtpAndCompleteTransaction`
**Description**: Verify OTP and complete the transaction
**Access**: Requires authentication

```graphql
mutation VerifyOtpAndCompleteTransaction($input: OtpVerificationInput!) {
  verifyOtpAndCompleteTransaction(input: $input) {
    transactionId
    status
    message
    userDetails {
      id
      email
      phone {
        number
        masked
      }
    }
    paymentDetails {
      accountNumber
      routingNumber
      cvv
      expiryDate
    }
  }
}
```

**Variables**:
```json
{
  "input": {
    "transactionId": "txn_**********",
    "phoneNumber": "+**********",
    "otp": "123456"
  }
}
```

## Authentication Queries & Mutations

### 9. Get Current User from Cookies

**Query**: `meCookies`
**Description**: Authenticate user from encrypted cookies
**Access**: Requires valid cookies

```graphql
query GetCurrentUserFromCookies {
  meCookies {
    id
    email
    firstName
    lastName
    role
    isAdmin
    permissions
    accountId
    active
    verifiedEmail
    verifiedPhone
    mfaEnabled
  }
}
```

### 10. Logout

**Mutation**: `logout`
**Description**: Clear authentication cookies and logout
**Access**: Requires authentication

```graphql
mutation Logout {
  logout
}
```

### 11. Refresh Authentication

**Mutation**: `refreshAuth`
**Description**: Refresh authentication tokens
**Access**: Requires valid refresh token

```graphql
mutation RefreshAuth {
  refreshAuth
}
```

### 12. Get Auth Status

**Query**: `authStatus`
**Description**: Get authentication service status
**Access**: Public

```graphql
query GetAuthStatus {
  authStatus {
    status
    service
    timestamp
    version
  }
}
```

## Input Types Reference

### CustomerFilterInput
```graphql
input CustomerFilterInput {
  search: String
  status: CustomerStatus
  type: CustomerType
  isEmailVerified: Boolean
  isPhoneVerified: Boolean
  isKycVerified: Boolean
  companyName: String
  country: String
  tags: [String!]
  createdAfter: DateTime
  createdBefore: DateTime
  lastLoginAfter: DateTime
  lastLoginBefore: DateTime
}
```

### PaginationInput
```graphql
input PaginationInput {
  skip: Float = 0
  take: Float = 20
}
```

### CreateCustomerInput
```graphql
input CreateCustomerInput {
  firstName: String!
  lastName: String!
  email: String!
  phone: String
  type: CustomerType
  status: CustomerStatus
  dateOfBirth: String
  companyName: String
  businessType: String
  taxId: String
  externalId: String
  notes: String
  tags: [String!]
}
```

### UpdateCustomerInput
```graphql
input UpdateCustomerInput {
  id: ID!
  firstName: String
  lastName: String
  email: String
  phone: String
  type: CustomerType
  status: CustomerStatus
  dateOfBirth: String
  companyName: String
  businessType: String
  taxId: String
  notes: String
  tags: [String!]
}
```

## Enums

### CustomerStatus
- `ACTIVE`: Customer is active and can perform transactions
- `INACTIVE`: Customer is inactive but not blocked
- `SUSPENDED`: Customer is temporarily suspended
- `PENDING_VERIFICATION`: Customer pending verification
- `BLOCKED`: Customer is permanently blocked

### CustomerType
- `INDIVIDUAL`: Individual customer
- `BUSINESS`: Business customer
- `ENTERPRISE`: Enterprise customer

### AddressType
- `HOME`: Home address
- `WORK`: Work address
- `BILLING`: Billing address
- `SHIPPING`: Shipping address
- `OTHER`: Other address type

### ContactType
- `PRIMARY`: Primary contact
- `SECONDARY`: Secondary contact
- `EMERGENCY`: Emergency contact
- `BUSINESS`: Business contact

## Error Handling

The GraphQL API returns structured errors with the following format:

```json
{
  "errors": [
    {
      "message": "Customer not found",
      "code": "CUSTOMER_NOT_FOUND",
      "path": ["customer"]
    }
  ],
  "data": null
}
```

Common error codes:
- `AUTHENTICATION_FAILED`: Invalid or missing authentication
- `AUTHORIZATION_FAILED`: Insufficient permissions
- `CUSTOMER_NOT_FOUND`: Customer does not exist
- `VALIDATION_ERROR`: Input validation failed
- `DUPLICATE_EMAIL`: Email already exists
- `INVALID_OTP`: OTP verification failed

## Testing the API

### Using GraphQL Playground

1. Start the backend server in development mode
2. Navigate to `http://localhost:3060/graphql`
3. Use the built-in documentation explorer
4. Test queries and mutations with the examples above

### Using curl

```bash
# Example: Get customers
curl -X POST http://localhost:3060/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "query { customers(page: 1, limit: 5) { data { id firstName lastName email } total } }"
  }'
```

### Authentication Testing

For testing purposes, some queries are marked as `@Public()`. In production, ensure proper authentication is enforced for all sensitive operations.

## Performance Considerations

- Use pagination for large datasets
- Specify only required fields in queries
- Use filters to reduce data transfer
- Consider using DataLoader for N+1 query prevention
- Monitor query complexity and depth

## Security Best Practices

- Always validate input data
- Use proper authentication for sensitive operations
- Implement rate limiting
- Log all data access for audit purposes
- Encrypt sensitive data in transit and at rest
- Validate user permissions for each operation
